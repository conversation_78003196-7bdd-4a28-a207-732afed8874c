/******************************

    00 - Home
    01 - Inner Page
		01 - Our History
		02 - Service Detail
        03 - Portfolio Single 
        04 - Blog Detail
             Sidebar Right
             Blog Classic
             Blog Single
        05 - Team Single
        06 - Contact Us
        07 - Comment Form
    
******************************/

/*----------------------------------------*/
/*  00 - Home
/*----------------------------------------*/
#page{
	overflow: hidden;
	position: relative;
}
/** Homepage 01 **/
.about-us-sec-one{
	padding: 40px;
}
.fadeIn {
    animation-name: fadeIn;
}
.animated.animated-slow {
    animation-duration: 2s;
}
.animated {
    animation-duration: 1.25s;
}
@keyframes fadeIn{
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
.about-us-one-left{
	background-image: url(../images/homepage-1/bg/icon-box-bg.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
	padding: 40px 40px 0px 40px;
	border-radius: 30px;
	margin-right: 20px;
}
.about-us-one-center-area{
	border-radius: 30px;
	padding: 20px 50px 20px 40px;
	margin: 0 10px;
	height: 100%;
}
.about-us-one-center-area .pbminfotech-ele-fid-style-2 .pbmit-fid-inner {
	margin-bottom: 65px;
}
.about-us-one-rightbox{
	position: relative;
	background-color: var(--pbmit-white-color);
	background-image: url(../images/homepage-1/bg/icon-box-bg-02.png);
    background-position: bottom center;
    background-repeat: no-repeat;
	margin-left: 20px;
	padding: 40px 0px 0px 40px;
	border-radius: 30px;
	display: flex;
	align-content: space-between;
	flex-wrap: wrap;
	height: 100%;
}
.about-us-one-rightbox .pbmit-bg-overlay{
	content: "";
	height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    position: absolute;
	border-radius: 30px;
	background-image: url(../images/homepage-1/bg/icon-bg-pattern-01.png);
    background-position: top right;
    background-repeat: no-repeat;
    opacity: 1;
}
.about-us-one-rightbox .pbmit-heading{
	font-size: 24px;
    font-weight: 700;
    line-height: 28px;
}
.about-us-one-rightbox .pbmit-icon-btn{
	width: 100%;
	text-align: right;
}
.pbmit-icon-btn .pbmit-btn-wrap{
	display: inline-table;
	padding: 10px 0 0 15px;
    border-radius: 30px 0 0 0;
    background-color: var(--pbmit-white-color);
	position: relative;
	z-index: 1;
}
.pbmit-bg-color-light .pbmit-icon-btn .pbmit-btn-wrap {
    background-color: var(--pbmit-light-color);
}
.pbmit-icon-btn .pbmit-btn-wrap:before,
.pbmit-icon-btn .pbmit-btn-wrap:after{
	content: "";
    position: absolute;
    background-color: transparent;
    bottom: 0;
    height: 40px;
    width: 20px;
    box-shadow: 0 15px 0 0 var(--pbmit-white-color);
}
.pbmit-icon-btn .pbmit-btn-wrap:before{
	right: 100%;
    border-bottom-right-radius: 15px;
}
.pbmit-bg-color-light .pbmit-icon-btn .pbmit-btn-wrap::after,
.pbmit-bg-color-light .pbmit-icon-btn .pbmit-btn-wrap::before{
    box-shadow: 0 15px 0 0 var(--pbmit-light-color);
}
.pbmit-icon-btn .pbmit-btn-wrap::after {
    top: -40px;
    right: 0;
    z-index: -1;
    border-bottom-right-radius: 20px;
}
.ihbox-one-bg{
	background-image: url(../images/homepage-1/bg/about-bg-icon.png);
    background-position: -40px 220px;
    background-repeat: no-repeat;
}
.about-one-left-bg{
	background-image: url(../images/homepage-1/bg/about-img.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
	margin: 0px 20px 0px 40px;
	border-radius: 30px;
	height: 100%;
}
.about-one-content{
	margin: 0px 40px 0px 20px;
	padding: 100px 120px 100px 120px;
	border-radius: 30px;
}
.about-one-content .fid-style-area{
	margin-bottom: 35px;
	padding-bottom: 35px;
	border-bottom: 1px solid #C0C6CE;
}
.about-one-content .pbmit-heading{
	font-size: 22px;
	line-height: 22px;
	margin-bottom: 20px;
}
.about-one-content .list-group{
	padding-left: 35px;
}
.service-one-bg{
	background-image: url(../images/homepage-1/bg/service-bg-icon.png);
    background-position: 1740px 350px;
    background-repeat: no-repeat;
}
.swiper-btn-custom{
	position: relative;
}
.swiper-btn-custom:after{
	content: '|';
    position: absolute;
    top: 2px;
    left: 50%;
    z-index: 10;
    color: rgba(var(--pbmit-white-color-rgb), .5);
    -khtml-transform: translateX(-50%) translateY(-60%);
    -moz-transform: translateX(-50%) translateY(-60%);
    -ms-transform: translateX(-50%) translateY(-60%);
    -o-transform: translateX(-50%) translateY(-60%);
    transform: translateX(-50%) translateY(-60%);
}
.fid-one-area{
	border-top: 1px solid #E5E5E5;
	padding-top: 100px;
	position: relative;
}
.fid-one-area .pbmit-text-editor{
	position: absolute;
	width: auto;
	max-width: 100%;
	top: -6px;
    color: #565656;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    line-height: 12px;
    letter-spacing: 1px;
	left: 45%;
}
.fid-one-area .pbmit-text-editor span{
	padding: 0px 30px 0px 30px;
	background-color: var(--pbmit-white-color);
}
.contact-one-bg{
	background-image: url(../images/homepage-1/bg/contact-form-bg.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
	margin: 0px 40px 0px 40px;
	padding: 100px 40px 100px 40px;
	border-radius: 30px;
}
.contact-one-bg .pbmit-appointment-form-inner {
	position: relative;
	border-radius: 30px;
	padding: 80px;
	z-index: 1;
}
.contact-one-bg .pbmit-appointment-form-inner:before{
	position: absolute;
    content: '';
    border-radius: 30px;
    padding: 80px;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
	background-color: var(--pbmit-blackish-color);
    -webkit-mask: url(../images/homepage-1/bg/contact-bg-shape.png) bottom right no-repeat;
    mask: url(../images/homepage-1/bg/contact-bg-shape.png) bottom right no-repeat;
}
.contact-one-bg form .input-button{
	display: block;
	position: relative;
}
.contact-one-bg form button{
	position: absolute;
    bottom: 0px;
    right: 105px;
    padding: 16px 55px;
    color: var(--pbmit-blackish-color);
    background-color: var(--pbmit-white-color);
}
.contact-one-bg form button svg path{
	stroke: var(--pbmit-blackish-color);
}
.testimonial-one-bg{
	background-image: url(../images/homepage-1/bg/testimonial-bg-icon.png);
    background-position: 0px 500px;
    background-repeat: no-repeat;
	position: relative;
}
.border-top,
.border-bottom{
	border-color: #E5E5E5;
}
.pricing-one-bg{
	background-image: url(../images/homepage-1/bg/priceing-bg-icon.png);
    background-position: 1190px 490px;
    background-repeat: no-repeat;
	padding: 100px 45px 100px 45px;
	border-radius: 30px;
}
.pricing-one-col-1{
	width: 63%;
}
.pricing-one-col-2{
	width: 37%;
	padding: 50px 0px 0px 50px;
}
/** Homepage 02 **/
.client-sec-two{
	padding: 40px 40px 0px 40px;
}
.about-sec-two{
	background-image: url(../images/homepage-2/bg/about-bg-icon-01.png);
    background-position: 1630px 400px;
    background-repeat: no-repeat;
}
.about-two-bg{
	background-image: url(../images/homepage-2/bg/about-bg-img.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
	border-radius: 30px;
	height: 100%;
	position: relative;
}
.about-two-bg .ihbox-style-area{
	position: absolute;
	left: 0;
	width: auto;
	bottom: 0;
	max-width: 100%;
}
.about-two-content{
	padding: 60px 0px 60px 60px;
}
.service-two-swiper-arrow .swiper-button-next, 
.service-two-swiper-arrow .swiper-button-prev{
	background-color: transparent;
	color: var(--pbmit-blackish-color);
	border: 1px solid var(--pbmit-blackish-color);
	border-left: none;
}
.service-two-swiper-arrow .swiper-button-next:hover, 
.service-two-swiper-arrow .swiper-button-prev:hover{
	color: var(--pbmit-global-color);
}
.service-two-swiper-arrow:after{
	color: rgba(var(--pbmit-blackish-color-rgb),.5);
}
.ihbox-two-bg{
	background-image: url(../images/homepage-2/bg/icon-bg-pattern.png);
    background-position: top left;
    background-repeat: no-repeat;
	padding: 100px 40px 70px 40px;
	border-radius: 30px;
}
.pricing-two-bg{
	background-image: url(../images/homepage-2/bg/priceing-bg-mop.png);
    background-position: center left;
    background-repeat: no-repeat;
}
.testimonial-two-bg{
	background-image: url(../images/homepage-2/bg/testimonial-bg.jpg);
	background-position: center center;
    background-repeat: repeat;
    background-size: cover;
	margin-right: 20px;
	border-radius: 30px;
	height: 100%;
}
.testimonial-two-box{
	margin-left: 20px;
	padding: 100px 152px 100px 100px;
	border-radius: 30px;
	background-image: url(../images/homepage-2/bg/testimonial-bg-icon-01.png);
    background-position: 720px 730px;
    background-repeat: no-repeat;
}
.testimonial-two-box .swiper-buttons{
	position: absolute;
	right: 0;
	bottom: 40px;
}
.testimonial-two-box .swiper-buttons .swiper-button-prev,
.testimonial-two-box .swiper-buttons .swiper-button-next{
	position: absolute;
}
.testimonial-two-box .swiper-buttons .swiper-button-prev{
	right: 60px;
}
.testimonial-two-box .swiper-buttons .swiper-button-prev:before{
	content: '|';
    position: absolute;
    top: 50%;
    left: 0;
    z-index: 1;
    color: rgba(var(--pbmit-white-color-rgb), .5);
    -khtml-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    -o-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-55%);
}
.testimonial-two-box .fid-style-area{
	margin-top: 45px;
	padding-top: 45px;
	border-top: 1px solid #C0C6CE;
}
.appointment-two-bg{
	position: relative;
	background-image: url(../images/homepage-2/bg/action-bg-img.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
	padding: 130px 0px 100px 60px;
	border-radius: 30px;
}
.appointment-two-bg .ihbox-style-area{
	position: absolute;
	right: -104px;
	width: auto;
	top: 45%;
	max-width: 100%;
	transform: rotateZ(-90deg);
}
.appointment-two-bg .pbmit-ihbox-style-7 .pbmit-ihbox-box {
	display: inline-block;
	position: relative;
	padding: 20px 40px 0 40px;
	border-radius: 20px 20px 0 0;
	background-color: var(--pbmit-white-color);
}
.appointment-two-bg .pbmit-ihbox-style-7 .pbmit-ihbox-box::after,
.appointment-two-bg .pbmit-ihbox-style-7 .pbmit-ihbox-box::before {
	content: "";
	position: absolute;
	background-color: transparent;
	bottom: 0;
	height: 40px;
	width: 20px;
}
.appointment-two-bg .pbmit-ihbox-style-7 .pbmit-ihbox-box::before {
	box-shadow: 3px 15px 0 0 var(--pbmit-white-color);
}
.appointment-two-bg .pbmit-ihbox-style-7 .pbmit-ihbox-box::after{
	box-shadow: -1px 15px 0 0 var(--pbmit-white-color);
}
.appointment-two-bg .pbmit-ihbox-style-7 .pbmit-ihbox-box::before {
	right: 100%;
	border-bottom-right-radius: 15px;
}
.appointment-two-bg .pbmit-ihbox-style-7 .pbmit-ihbox-box::after {
	left: 100%;
	border-bottom-left-radius: 15px;
}
.appointment-two-bg .pbmit-ihbox-style-7 .pbmit-icon-type-icon {
	transform: rotate(45deg);
}
.blog-two-bg{
	background-image: url(../images/homepage-2/bg/blog-bg-icon.png);
    background-position: top left;
    background-repeat: no-repeat;
}
/** Homepage 03 **/ 
.pbmit-text-editor{
	font-size: 16px;
	font-weight: 400;
}
.service-sec-three{
	position: relative;
	background-image: url(../images/homepage-3/bg/service-bg-pattern.png);
    background-position: top left;
    background-repeat: no-repeat;
	margin: 100px 40px 0 40px;
	border-radius: 30px;
	padding: 100px 0 180px 0;
}
.service-sec-three:before{
	position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    content: "";
    display: block;
	border-radius: 30px;
	background-color: rgba(var(--pbmit-light-color-rgb),0.60);
}
.service-sec-three .pbmit-btn-wrap .pbmit-btn{
	padding: 18px 50px;
}
.service-sec-three .pbmit-btn-wrap{
	display: inline-table;
	position: absolute;
	bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    padding: 20px 30px 0 30px;
    border-radius: 30px 30px 0 0;
    background-color: var(--pbmit-white-color);
}
.service-sec-three .pbmit-btn-wrap:before,
.service-sec-three .pbmit-btn-wrap:after{
	content: "";
    position: absolute;
    background-color: transparent;
    bottom: 0;
    height: 40px;
    width: 20px;
    box-shadow: 0 15px 0 0 var(--pbmit-white-color);
}
.service-sec-three .pbmit-btn-wrap:before{
	right: 100%;
    border-bottom-right-radius: 15px;
}
.service-sec-three .pbmit-btn-wrap:after{
	left: 100%;
    border-bottom-left-radius: 15px;
}
.ihbox-three-bg{
	background-image: url(../images/homepage-3/bg/icon-bg-mop.png);
    background-position: center left;
    background-repeat: no-repeat;
}
.portfolio-sec-three .container-fluid{
	padding: 0 30px;
}
.portfolio-sec-three .swiper-slide.even .pbmit-portfolio-style-2:nth-child(2){
	margin-top: 30px;
}
.testimonial-three-bg{
	background-image: url(../images/homepage-3/bg/testimonial-bg-icon.png);
    background-position: 99% 35%;
    background-repeat: no-repeat;
}
.contact-three-bg{
	background-image: url(../images/homepage-3/bg/contact-bg-img.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
	margin: 0px 20px 0px 40px;
	border-radius: 30px;
	height: 100%;
}
.contact-three-form{
	margin: 0px 40px 0px 20px;
	padding: 100px 120px 0px 120px;
	border-radius: 30px;
}
.contact-three-form form{
	position: relative;
	padding-bottom: 80px;
}
.contact-three-form .input-button{
	display: inline-table;
	position: absolute;
	bottom: 0;
	right: 0;
	padding: 10px 15px 0 15px;
	border-radius: 30px 30px 0 0;
	background-color: var(--pbmit-white-color);
}
.contact-three-form .input-button:before,
.contact-three-form .input-button:after{
	content: "";
    position: absolute;
    background-color: transparent;
    bottom: 0;
    height: 40px;
    width: 20px;
    box-shadow: 0 15px 0 0 var(--pbmit-white-color);
}
.contact-three-form .input-button:before{
	right: 100%;
    border-bottom-right-radius: 15px;
}
.contact-three-form .input-button:after{
	left: 100%;
    border-bottom-left-radius: 15px;
}
.contact-three-form .input-button .pbmit-btn{
	padding: 16px 55px;
}

/** Homepage 04 **/ 
.pbmit-text-editor.style-1{
	font-family: "Roboto", sans-serif;
}
.about-four-list-group{
	display: grid;
    grid-template-columns: repeat(2, 1fr);
}
.about-four-bg{
	position: relative;
	background-image: url(../images/homepage-4/bg/about-bg.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
	border-radius: 30px;
	height: 100%;
}
.about-four-bg .ihbox-style-area{
	position: absolute;
	width: auto;
	bottom: 0;
	max-width: 100%;
}
.service-sec-four{
	margin: 0 40px;
	border-radius: 30px;
}
.ihbox-four-bg{
	background-image: url(../images/homepage-4/bg/about-bg-icon-01.png);
    background-position: 1600px 350px;
    background-repeat: no-repeat;
}
.ihbox-four-leftbox .about-img{
	-webkit-mask-image: url(../images/homepage-4/bg/about-img-shape-01.png);
    -webkit-mask-size: contain;
    -webkit-mask-position: center center;
    -webkit-mask-repeat: no-repeat;
}
.ihbox-four-leftbox .fid-style-box{
	width: auto;
	max-width: 100%;
	bottom: 0;
	position: absolute;
}
.ihbox-four-rightbox{
	padding: 0px 130px 0px 110px;
}
.pbmit-element-portfolio-style-3 .swiper-wrapper{
	width: 100%;
    animation: marquee-left 25s linear infinite;
    will-change: transform;
    white-space: nowrap;
}
@keyframes marquee-left{
	0% {
		transform: translate(0, 0);
	}
	
	100% {
		transform: translate(-100%, 0);
	}
}
.pbmit-portfolio-bottom{
	margin-top: 30px;
}
.pbmit-portfolio-bottom .pbmit-element-portfolio-style-3 .swiper-wrapper{
	width: 100%;
    animation: marquee-right 25s linear infinite;
    will-change: transform;
    white-space: nowrap;
}
@keyframes marquee-right{
	0% {
		transform: translate(-100%, 0);
	}
	
	100% {
		transform: translate(0, 0);
	}
}
.pbmit-portfolio-content {
	position: absolute;
	left: 41%;
	width: auto;
	top: 31%;
	max-width: 100%;
}
.pbmit-portfolio-content .pbmit-heading{
	width: 330px;
    height: 330px;
    border-radius: 100%;
    text-align: center;
    position: relative;
    background-color: var(--pbmit-global-color);
	z-index: 1;
}
.pbmit-portfolio-content .pbmit-heading .pbmit-title{
	font-size: 30px;
    line-height: 34px;
    margin: 0;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    font-weight: 500;
    color: var(--pbmit-white-color);
    -khtml-transform: translateX(0%) translateY(-50%);
    -moz-transform: translateX(0%) translateY(-50%);
    -ms-transform: translateX(0%) translateY(-50%);
    -o-transform: translateX(0%) translateY(-50%);
    transform: translateX(0%) translateY(-50%);
}
.faq-four-bg-img{
	background-image: url(../images/homepage-4/bg/faq-bg-img.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
	border-radius: 30px;
	height: 100%;
	margin-right: 10px;
}
.faq-four-area{
	padding: 80px 150px 80px 100px;
	border-radius: 30px;
	margin-left: 10px;
}

/** Homepage 05 **/
.about-five-leftbox{
	position: relative;
	background-color: var(--pbmit-blackish-color);
	margin: 0px 10px 0px 40px;
	padding: 230px 140px 170px 100px;
	border-radius: 30px;
	background-image: url(../images/homepage-5/bg/bg-star-01.png);
    background-position: top left;
    background-repeat: no-repeat;
	height: 100%;
}
.about-five-bg-overlay{
	position: absolute;
	height: 100%;
	width: 100%;
	top: 0;
	left: 0;
	background-image: url(../images/homepage-5/bg/bg-star-02.png);
    background-position: bottom right;
    background-repeat: no-repeat;
}
.about-five-leftbox .star-icon-image{
	position: absolute;
	width: auto;
	max-width: 100%;
	right: 50%;
	top: -20px;
}
.about-five-subtitle{
	position: relative;
	font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    line-height: 14px;
    letter-spacing: 1px;
	color: var(--pbmit-white-color);
}
.about-five-leftbox .pbmit-heading{
	border-bottom: 1px solid #364C61;
	margin: 20px 0px 30px 0px;
}
.about-five-leftbox .pbmit-heading .pbmit-title{
	font-size: 66px;
	line-height: 66px;
	margin-bottom: 20px;
	color: var(--pbmit-white-color);
}
.about-five-heading-desc{
	font-size: 18px;
	font-weight: 400;
	line-height: 24px;
	color: var(--pbmit-white-color);
}
.about-five-right-box{
	margin: 0px 40px 0px 10px;
}
.about-five-right-box .about-img-shape{
	-webkit-mask-image: url(../images/homepage-5/bg/about-img-shape.webp);
    -webkit-mask-size: contain;
    -webkit-mask-position: center center;
    -webkit-mask-repeat: no-repeat;
}
.about-five-right-box .pbmit-ihbox-wrap{
	margin-right: 20px;
	padding: 50px 45px 50px 45px;
	border-radius: 30px;
	background-color: var(--pbmit-light-color);
}
.about-five-right-box .pbmit-ihbox-wrap .pbmit-ihbox-icon i{
	font-size: 18px;
	color: var(--pbmit-global-color);
}
.about-five-right-box .pbmit-ihbox-wrap .pbmit-ihbox-icon i:before{
	margin: 0;
}
.about-five-right-box .pbmit-ihbox-wrap .pbmit-text-wrap{
	margin-top: 20px;
}
.about-five-right-box .pbmit-ihbox-wrap .pbmit-text-wrap .pbmit-title{
	font-size: 16px;
	line-height: 16px;
	margin-bottom: 20px;
}
.about-five-right-box .pbmit-ihbox-wrap .pbmit-ihbox-icon-type-image{
	padding-top: 100px;
}
.about-five-right-box .pbmit-ihbox-second-wrap{
	padding: 80px 20px 80px 20px;
	background-color: var(--pbmit-light-color);
	border-radius: 30px;
	height: 100%;
}
.about-five-right-box .pbmit-ihbox-second-wrap .pbmit-heading .pbmit-title{
	font-size: 16px;
	line-height: 24px;
}
.about-five-right-box .about-img-bg{
	background-image: url(../images/homepage-5/bg/about-img-bg-01.webp);
    background-position: top center;
    background-repeat: no-repeat;
    background-size: cover;
	border-radius: 30px;
	padding: 200px 0;
	margin: -85px 0px 0px 25px;
}
.client-sec-five{
	padding: 60px 0;
	margin: 0 40px;
}
.service-five-bg{
	background-image: url(../images/homepage-5/bg/service-bg-icon.webp);
    background-position: -60px 240px;
    background-repeat: no-repeat;
}
.appointment-five-left-col{
	width: 70%;
}
.appointment-five-right-col{
	width: 30%;
}
.appointment-five-left-box{
	background-image: url(../images/homepage-5/bg/action-bg-img-01.png);
    background-position: bottom left;
    background-repeat: no-repeat;
	background-color: var(--pbmit-global-color);
	padding: 90px 30px 60px 70px;
	margin-right: 20px;
	border-radius: 30px;
}
.appointment-five-left-box .pbmit-btn-blackish:hover{
	color: var(--pbmit-blackish-color);
	background-color: var(--pbmit-white-color);
}
.appointment-five-right-col .wrapper{
	display: flex;
	align-content: space-between;
	flex-wrap: wrap;
	height: 100%;
	padding: 50px 50px 30px 50px;
	margin-left: 20px;
	border-radius: 30px;
	background-color: var(--pbmit-blackish-color);
}
.appointment-five-right-col .action-image,
.appointment-five-right-col .pbmit-heading{
	width: 100%;
}
.appointment-five-right-col .wrapper .pbmit-heading .pbmit-title{
	font-size: 20px;
    font-weight: 500;
    line-height: 24px;
	color: var(--pbmit-white-color);
}
/*------------------- ---------------------*/
/*01 - Inner Page 
/*----------------------------------------*/
/** 01 - Our History **/
.pbmit-element-timeline-style-1 .pbmit-timeline-year{
	font-size: 18px;
	line-height: 24px;
	margin-bottom: 5px;
	letter-spacing: 0.5px;
	color: var(--pbmit-global-color);
}
.pbmit-element-timeline-style-1 .pbmit-timeline-title{
	font-size: 24px;
	line-height: 28px;
	margin-bottom: 10px;
	padding: 0 50px;
	text-transform: capitalize;
}
.pbmit-element-timeline-style-1 .pbmit-timeline-desc{
	margin: 0;
	padding: 0 90px;
	line-height: 22px;
}
.pbmit-element-timeline-style-1 .pbmit-timeline-wrapper{
	display: grid;
	width: 100%;
	height: auto;
	position: relative;
	justify-items: center;
	grid-template-rows: 1fr 1fr;
	text-align: center;
	grid-gap: 160px;
}
.pbmit-element-timeline-style-1 .pbmit-same-height.steps-content_wrap{
	justify-content: flex-start;
}
.pbmit-element-timeline-style-1 .pbmit-slide-even .pbmit-same-height.steps-content_wrap{
	justify-content: flex-end;
}
.pbmit-element-timeline-style-1 .pbmit-heading-subheading{
	margin-bottom: 80px;
}
.pbmit-element-timeline-style-1 .steps-dot .dot{
	background-color: var(--pbmit-blackish-color);
	display: block;
	line-height: normal;
	font-size: 14px;
	margin: auto;
	width:  10px;
	height:  10px;
	transform: rotate(45deg);
	z-index: 2;
	position: relative;
}
.pbmit-element-timeline-style-1 .pbmit-timeline-wrapper .steps-dot{
	position: absolute;
	z-index: 2;
	width: 100%;
	left: 0;
	bottom: 0;
	top: 0;
	right: 0;
	height: -moz-fit-content;
	height: fit-content;
	margin: auto !important;
}
.pbmit-element-timeline-style-1 .steps-dot .dot::after{
	content: "";
	position: absolute;
	left: -11px;
	right: 0;
	bottom: 0;
	width: 1px;
	height: 40px;
	margin: 0;
	background: rgb(var(--pbmit-blackish-color-rgb), .90);
	transform: rotate(-45deg);
}
.pbmit-element-timeline-style-1 .pbmit-slide-even .steps-dot .dot::after{
	left: 19px;
	top: 0;
	bottom: auto;
}
.pbmit-element-timeline-style-1 .steps-dot .steps-dot-line{
	color: transparent;
	line-height: 10px;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
} 
.pbmit-element-timeline-style-1 .steps-dot .steps-dot-line::before,
.pbmit-element-timeline-style-1 .steps-dot .steps-dot-line::after{
	content: "";
	width: calc(50% + 10px * 100);
	height: 1px;
	display: block;
	position: absolute;
	top: 0;
	bottom: 0;
	margin: auto;
	background-repeat: no-repeat;
}
.pbmit-element-timeline-style-1 .steps-dot .steps-dot-line::after{
	left: 50%;
	background: linear-gradient(90deg, rgb(var(--pbmit-blackish-color-rgb), 0.05), rgb(var(--pbmit-blackish-color-rgb), 0.05));
}
.pbmit-element-timeline-style-1 .steps-dot .steps-dot-line::before{
	right: 50%;
	background: linear-gradient(90deg, rgb(var(--pbmit-blackish-color-rgb), 0.05), rgb(var(--pbmit-blackish-color-rgb), 0.05));
}
.pbmit-element-timeline-style-1 .pbmit-same-height{
	display: flex;
	flex-direction: column;
	justify-content: end;
}
.pbmit-element-timeline-style-1 .pbmit-slide-even.swiper-slide-duplicate:nth-last-child(2) .pbmit-feature-image{
	order: 0;
}
.pbmit-element-timeline-style-1 .pbmit-slide-even .pbmit-feature-image{
	order: 1;
	justify-content: start;
	transition: all 0.4s, opacity 0.4s 0.9s, transform 0.4s 0.9s;
}
.pbmit-element-timeline-style-1 .pbmit-timeline-wrapper:not(.pbmit-slide-even) .pbmit-feature-image{
	transition: all 0.4s, opacity 0.4s 0.9s, transform 0.4s 0.9s;
} 
.pbmit-element-timeline-style-1 .pbmit-feature-image img{
	border-radius: 30px;
}
.pbmit-element-timeline-style-1 .steps-dot,
.pbmit-element-timeline-style-1 .pbmit-feature-image,
.pbmit-element-timeline-style-1 .steps-content_wrap{
	opacity: 1;
}
.pbmit-element-timeline-style-1 .swiper-button-next.swiper-button-disabled, 
.pbmit-element-timeline-style-1 .swiper-button-prev.swiper-button-disabled{
	opacity: 1;
}
.pbmit-bg-color-blackish .pbmit-element-timeline-style-1 .pbmit-timeline-title,
.pbmit-bg-color-blackish .pbmit-element-timeline-style-1{
	color: var(--pbmit-white-color);
}
.pbmit-bg-color-blackish .pbmit-element-timeline-style-1 .steps-dot .steps-dot-line::after{
	background: linear-gradient(90deg, rgb(var(--pbmit-white-color-rgb), .10), rgb(var(--pbmit-white-color-rgb), .10));
}
.pbmit-bg-color-blackish .pbmit-element-timeline-style-1 .steps-dot .steps-dot-line::before{
	background: linear-gradient(90deg, rgb(var(--pbmit-white-color-rgb), .10), rgb(var(--pbmit-white-color-rgb), .10));
}
.pbmit-bg-color-blackish .pbmit-element-timeline-style-1 .steps-dot .dot{
	background-color: var(--pbmit-white-color);
}
.pbmit-bg-color-blackish .pbmit-element-timeline-style-1 .steps-dot .dot::after{
	background: rgb(var(--pbmit-white-color-rgb), .40);
}
.pbmit-bg-color-blackish .pbmit-element-timeline-style-1 .pbmit-timeline-desc {
	color: rgb(var(--pbmit-white-color-rgb), .60);
}

/** 02 - Service Detail **/
.service-details > .container > .row{
	-ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
}
.service-details .all-post-list li{
    margin-bottom: 1px;
	border-radius: 10px;
	transition: all 0.4s ease-in;
	position: relative;
	line-height: 20px;
}
.service-details .all-post-list li a{
	padding: 18px 35px 18px 40px;
    display: block;
    position: relative;
    border-radius: 30px;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    color: var(--pbmit-blackish-color);
    -webkit-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out;
}
.service-details .all-post-list li a:before{
	position: absolute;
    content: '';
    left: 20px;
    height: 5px;
    width: 5px;
    top: 50%;
    border-radius: 50%;
    background-color: var(--pbmit-global-color);
    -khtml-transform: translateX(0%) translateY(-50%);
    -moz-transform: translateX(0%) translateY(-50%);
    -ms-transform: translateX(0%) translateY(-50%);
    -o-transform: translateX(0%) translateY(-50%);
    transform: translateX(0%) translateY(-50%);
    -webkit-transition: all 0.4s ease-in;
    -ms-transition: all 0.4s ease-in;
    -o-transition: all 0.4s ease-in;
    transition: all 0.4s ease-in;
}
.service-details .all-post-list li a:after{
	position: absolute;
    content: "\e8dd";
    font-family: 'pbminfotech-base-icons';
    right: 20px;
    font-size: 9px;
    top: 50%;
    z-index: 1;
    transition: all 0.4s ease;
    font-weight: 500;
    opacity: 0;
    visibility: hidden;
    color: rgba(var(--pbmit-blackish-color-rgb), .7);
    -khtml-transform: translateX(0%) translateY(-50%);
    -moz-transform: translateX(0%) translateY(-50%);
    -ms-transform: translateX(0%) translateY(-50%);
    -o-transform: translateX(0%) translateY(-50%);
    transform: translateX(0%) translateY(-50%);
}
.service-details .all-post-list li:hover a,
.service-details .all-post-list li.post-active a{
	color: var(--pbmit-white-color);
	background-color: var(--pbmit-blackish-color);
}
.service-details .all-post-list li.post-active a:after,
.service-details .all-post-list li:hover a:after{
	opacity: 1;
	visibility: visible;
}
.service-details .all-post-list li.post-active a:after,
.service-details .all-post-list li a:hover:after{
	color: var(--pbmit-white-color);
}
.service-sidebar .widget:last-child{
	background-color: transparent;
	margin-bottom: 0px;
	padding: 0;
	border: none;
}
.service-sidebar .widget.pbmit-download-info {
	padding: 30px;
	background-color: var(--pbmit-light-color);
}
.widget .pbmit-download {
    display: block;
    padding-top: 10px;
}
.widget .pbmit-download .pbmit-item-download:first-child {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(var(--pbmit-blackish-color-rgb), 0.15);
}
.widget a {
    -webkit-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out;
}
.widget .pbmit-item-download .pbmit-download-wrap,
.widget .pbmit-item-download .pbmit-download-content{
	position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.widget .pbmit-download .pbmit-item-download a .pbmit-download-content i{
	font-size: 40px;
    line-height: 40px;
    color: var(--pbmit-global-color);
}
.widget .pbmit-item-download .pbmit-title-wrap {
    padding-left: 15px;
}
.widget .pbmit-item-download .pbmit-download-wrap .pbmit-download-title {
    font-size: 16px;
    line-height: 16px;
    margin-bottom: -2px;
    color: var(--pbmit-blackish-color);
}
.widget .pbmit-item-download .pbmit-download-wrap span {
    font-size: 14px;
    line-height: 14px;
    color: #565656;
}
.widget .pbmit-download .pbmit-item-download a .pbmit-download-item i {
    font-size: 26px;
    line-height: 26px;
    color: var(--pbmit-blackish-color);
    -webkit-transition: all 0.4s ease-in;
    -ms-transition: all 0.4s ease-in;
    -o-transition: all 0.4s ease-in;
    transition: all 0.4s ease-in;
}
.widget .pbmit-download .pbmit-item-download a:hover .pbmit-download-item i {
    color: var(--pbmit-global-color);
}
.service-details .pbmit-service-feature-image img{
	margin-bottom: 50px;
    border-radius: 30px;
}
.service-details .pbmit-entry-content .pbmit-desc{
	font-size: 16px;
    font-weight: 400;
    line-height: 24px;
}
.service-details .pbmit-entry-content .service-single-img-02{
	background-image: url(../images/service/service-single-02.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
	border-radius: 30px;
	padding: 170px 0;
}
.service-details .list-group-borderless .list-group-item:not(:last-child){
	padding-bottom: calc(10px/2);
}

/** 03 - Portfolio Single **/
.pbmit-portfolio-single .pbmit-featured-img-wrapper img{
	border-radius: 30px;
}
.pbmit-single-project-details-list {
    padding: 50px 0;
}
.pbmit-single-project-details-list .pbmit-portfolio-lines-ul{
	display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 30px;
    border-radius: 20px;
    background-color: var(--pbmit-light-color);
}
.pbmit-single-project-details-list .pbmit-portfolio-lines-ul li{
	text-align: center;
    width: 100%;
    position: relative;
}
.pbmit-portfolio-lines-wrapper .pbmit-portfolio-line-title{
	font-size: 17px;
    line-height: 26px;
    text-transform: capitalize;
    position: relative;
    letter-spacing: 0.5px;
    color: var(--pbmit-blackish-color);
}
.pbmit-portfolio-lines-wrapper .pbmit-portfolio-line-value {
    display: block;
    font-size: 16px;
}
.pbmit-portfolio-lines-wrapper .pbmit-portfolio-line-li:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 1px;
    height: 100%;
    background-color: var(--pbmit-global-color);
}
.pbmit-portfolio-single .pbmit-entry-content .ihbox-style-area{
	padding: 40px 100px 40px 40px;
	border-radius: 30px;
}
.pbmit-portfolio-single .post-navigation{
	margin-top: 70px;
	border-top: 1px solid rgba(var(--pbmit-blackish-color-rgb),0.10);
}
.post-navigation .nav-links {
    margin-top: 0;
    padding: 40px 0 8px;
    display: flex;
    align-items: normal;
    width: 100%;
}
.post-navigation .nav-previous{
	width: 50%;
	margin-right: auto !important;
	position: relative;
}
.post-navigation .nav-links a{
	display: block;
    text-align: left;
    width: 300px;
    transition: all 0.3s ease-in;
}
.post-navigation .pbmit-post-nav-icon{
	display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 20px;
    line-height: 20px;
    margin-bottom: 5px;
}
.post-navigation .nav-links .nav-previous .pbmit-post-nav-icon {
    justify-content: flex-start;
}
.post-navigation .nav-links .pbmit-post-nav-icon i {
    color: var(--pbmit-global-color);
    transition: all 0.3s ease-in;
}
.post-navigation .nav-links .pbmit-post-nav-head {
    font-size: 13px;
    text-transform: uppercase;
    transition: all 0.3s ease-in;
    margin-left: 0;
    color: var(--pbmit-global-color);
    letter-spacing: 0;
}
.post-navigation .nav-links .pbmit-post-nav-wrapper {
    display: block;
}
.post-navigation .nav-links .nav-previous .pbmit-post-nav-wrapper {
    text-align: left;
}
.nav-links .pbmit-post-nav-wrapper {
    text-align: left;
    word-break: break-word;
}
.post-navigation .nav-links .nav-title {
    font-size: 20px;
    line-height: 26px;
    margin-top: 5px;
    text-transform: none;
    -webkit-transition: all .25s ease-in-out;
    -ms-transition: all .25s ease-in-out;
    -o-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out;
}
.post-navigation .nav-links .nav-next {
    width: 50%;
	text-align: right;
    margin-left: auto;
}
.post-navigation .nav-links .nav-next a{
	display: block;
    text-align: right;
    margin-left: auto;
}
.post-navigation .nav-links .nav-next .pbmit-post-nav-wrapper{
	text-align: right;
}
.post-navigation .nav-links .nav-next .pbmit-post-nav-head{
	margin-right: 8px;
}
.post-navigation .nav-links a:hover .pbmit-post-nav-head,
.post-navigation .nav-links a:hover .pbmit-post-nav-icon i{
	color: var(--pbmit-blackish-color);
}

/** 04 - Blog Detail**/

/** Sidebar Right **/
.sidebar .widget{ 
    padding: 30px;
    margin-bottom: 45px;
    border: 1px solid rgba(var(--pbmit-blackish-color-rgb), 0.15);
    border-radius: 30px;
    background-color: transparent;
}
.sidebar .widget:last-child{
    margin-bottom: 0;
}
.sidebar .widget.widget-search{
	border: none;
	background-color: var(--pbmit-blackish-color);
}
.sidebar .widget .widget-title{ 
	display: block;
	padding-left: 55px;
	margin-bottom: 20px;
    position: relative;
    width: auto;
	font-size: 24px;
    line-height: 30px;
    letter-spacing: 0px;
	padding-left: 30px;
    padding-bottom: 0;
	text-transform: capitalize;
	color: var(--pbmit-blackish-color);
} 
.sidebar .widget.widget-search .widget-title{
	color: var(--pbmit-white-color);
}
.sidebar .widget .widget-title:before{
	content: "\e820";
    font-family: 'pbminfotech-base-icons';
    position: absolute;
    left: 8px;
    top: 50%;
    font-size: 18px;
    line-height: 18px;
    font-weight: normal;
    color: var(--pbmit-global-color);
    -khtml-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    -o-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
}
.widget .search-form {
    position: relative;
}
.widget .search-form input{
	background-color: var(--pbmit-white-color);
	height: 60px;
    border: 1px solid rgba(var(--pbmit-white-color-rgb),0.2);
    padding: 0px 30px;
    padding-right: 50px;
    background-color: transparent;
    font-size: 15px;
    border-radius: 40px;
	display: block;
    width: 100%;
}
.widget .search-form input::placeholder{
	color: rgba(var(--pbmit-white-color-rgb), .6);
}
.widget .search-form .search-submit{
	position: absolute;
    top: 3px;
    right: 3px;
    width: 55px;
    height: 55px;
    font-size: 16px;
    z-index: 1;
    padding: 0;
    margin: 0;
    border: none;
    border-radius: 50%;
    outline: none;
    background: transparent;
    color: var(--pbmit-white-color);
}
.widget .search-form .search-submit:after{
	position: absolute;
    font-family: 'pbminfotech-base-icons';
    content: '\e80d';
    right: 50%;
    top: 50%;
    font-size: 16px;
    line-height: normal;
    font-weight: normal;
    color: rgba(var(--pbmit-white-color-rgb), 0.2);
    -webkit-transform: translateX(50%) translateY(-50%);
    -ms-transform: translateX(50%) translateY(-50%);
    transform: translateX(50%) translateY(-50%);
}
.widget .search-form .search-submit:hover{
	background: var(--pbmit-blackish-color);
}
.widget .search-form .search-submit:hover:after{
	color: var(--pbmit-white-color);
}
.sidebar .widget-categories li{
	position: relative;
	line-height: 20px;
}
.sidebar .widget-categories .pbmit-cat-li{
	display: flex;
    align-items: center;
	justify-content: space-between;
    color: rgba(var(--pbmit-blackish-color-rgb),.5);
    border-bottom: 1px solid rgba(var(--pbmit-blackish-color-rgb),0.05);
	-webkit-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out;
}
.sidebar .widget-categories ul li:last-child .pbmit-cat-li{
	border-bottom: none;
}
.sidebar .widget-categories .pbmit-cat-li a{
	font-size: 13px;
    line-height: 23px;
    position: relative;
	flex: 1;
	padding: 15px 0;
	font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
	transition: all 0.4s ease;
	color: #565656;
}
.sidebar .widget-categories .pbmit-cat-li a:before{
	position: absolute;
    content: "";
    left: 0;
    top: 50%;
    z-index: 1;
    width: 6px;
    height: 6px;
    border-radius: 100%;
    background-color: var(--pbmit-global-color);
    -webkit-transform: translateY(-55%);
    -moz-transform: translateY(-55%);
    -o-transform: translateY(-55%);
    transform: translateY(-55%);
    transition: all 0.4s ease;
    opacity: 0;
    visibility: hidden;
}
.sidebar .widget-categories .pbmit-brackets{
    font-size: 14px;
	transition: all .25s ease-in-out;
}
.sidebar .widget-categories .pbmit-cat-li>a:hover{
	padding-left: 20px;
	color: var(--pbmit-global-color);
}
.sidebar .widget-categories .pbmit-cat-li>a:hover:before{
	opacity: 1;
	visibility: visible;
}
.sidebar .widget-categories .pbmit-cat-li a:hover + .pbmit-brackets{
	color: var(--pbmit-global-color);
}
.widget-recent-post .recent-post-list-li:first-child{
	margin-top: 0px;
    padding-top: 0px;
	border-top: none;
}
.widget-recent-post .recent-post-list-li{
	display: flex!important;
    align-items: center;
    width: 100%;
	margin-top: 18px;
	padding-bottom: 18px;
	line-height: 20px;
	border-bottom: 1px solid rgba(var(--pbmit-blackish-color-rgb),0.10);
}
.widget-recent-post .recent-post-list-li:last-child{
	border-bottom: none;
	padding-bottom: 10px;
}
.widget-recent-post .recent-post-list-li>a{
	width: 91px;
    flex-shrink: 0;
    margin-right: 20px;
	display: inline-block;
}
.widget-recent-post .recent-post-list-li>a img{
	border-radius: 20px;
}
.widget-recent-post .pbmit-rpw-title a{
	margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-transform: capitalize;
    font-size: 16px;
    line-height: 20px;
}
.widget-recent-post .pbmit-rpw-title a:hover{
	color: var(--pbmit-global-color);
}
.pbmit-rpw-content .pbmit-rpw-date a{
	color: var(--pbmit-global-color);
    font-size: 14px;
    margin-bottom: 0px;
    text-transform: uppercase;
}
.widget ul a {
    display: inline-block;
}
.widget.pbmit-service-ad .textwidget:before,
.widget.pbmit-service-ad .textwidget:after{
	content: "";
    display: table;
}
.widget.pbmit-service-ad .textwidget:after{
	clear: both;
}
.widget.pbmit-service-ad{
	background-image: url(../images/bg/ads-bg-img.png);
	background-size: cover;
    background-position: center center;
    background-color: var(--pbmit-blackish-color);
    border: none;
	padding: 0;
}
.widget.pbmit-service-ad .pbmit-service-ads{
	position: relative;
    overflow: hidden;
    text-align: center;
    padding: 120px 30px;
    border-radius: 30px;
	z-index: 1;
}
.pbmit-service-ads .pbmit-ads-call{
	padding: 5px 15px;
    display: inline-block;
    border-radius: 50px;
    margin-bottom: 20px;
    color: var(--pbmit-white-color);
    border: 1px solid var(--pbmit-white-color);
}
.pbmit-service-ads .pbmit-ads-subtitle {
    font-size: 34px;
    line-height: 34px;
    margin-bottom: 0;
    color: var(--pbmit-global-color);
}
.pbmit-service-ads .pbmit-ads-title {
    font-size: 40px;
    line-height: 40px;
	margin-bottom: 0;
    color: var(--pbmit-white-color);
}
.pbmit-service-ads .pbmit-btn{
	margin-top: 30px;
}
.pbmit-service-ads .pbmit-btn:hover{
	color: var(--pbmit-blackish-color);
	background-color: var(--pbmit-white-color);
}
aside.widget.widget-tag-cloud a,
.pbmit-blog-meta-bottom .pbmit-meta-tags a{
    line-height: 30px;
	margin: 0 4px 12px 0;
    display: inline-block;
    font-size: 13px !important;
	font-weight: 600;
    padding: 0 15px;
    height: 30px;
    letter-spacing: .5px;
    text-transform: uppercase;
    -webkit-transition: 0.4s ease-out 0s;
    -o-transition: 0.4s ease-out 0s;
    transition: 0.4s ease-out 0s;
    color:#565656;
    background-color: var(--pbmit-light-color);
    border-radius: 30px;
}
aside.widget.widget-tag-cloud a:hover,
.pbmit-blog-meta-bottom .pbmit-meta-tags a:hover{
	color: var(--pbmit-white-color);
	background-color: var(--pbmit-blackish-color);
}

/** Blog Classic **/
.site-content{
	padding-top: 80px;
	padding-bottom: 80px;
}
.post.blog-classic{
    position: relative;
	margin-bottom: 60px;
}
.post.blog-classic .pbmit-img-wrapper{
	position: relative;
}
.post.blog-classic .pbmit-featured-wrapper img{
	border-radius: 30px;
}
.post.blog-classic:last-child {
    margin-bottom: 0px;
}
.post.blog-classic .pbmit-featured-img-wrapper{
	position: relative;
    margin-bottom: 30px;
}
.post.blog-classic .pbmit-img-wrapper .pbmit-meta-date{
	position: absolute;
    bottom: 0;
    left: 50px;
    padding: 12px 30px 12px 30px;
    border-radius: 30px 30px 0 0;
    background-color: var(--pbmit-white-color);
}
.post.blog-classic .pbmit-img-wrapper .pbmit-meta-date:before,
.post.blog-classic .pbmit-img-wrapper .pbmit-meta-date:after{
	content: "";
    position: absolute;
    background-color: transparent;
    bottom: 0;
    height: 40px;
    width: 20px;
    box-shadow: 0 20px 0 0 var(--pbmit-white-color);
}
.post.blog-classic .pbmit-img-wrapper .pbmit-meta-date:before{
	right: 100%;
    border-bottom-right-radius: 20px;
}
.post.blog-classic .pbmit-img-wrapper .pbmit-meta-date:after{
	left: 100%;
    border-bottom-left-radius: 20px;
}
.post.blog-classic .pbmit-img-wrapper .pbmit-meta-date>i{
	display: none;
}
.post.blog-classic .pbmit-img-wrapper .pbmit-meta-date a{
	font-size: 14px;
    line-height: 20px;
    color: #565656;
    text-transform: uppercase;
}
.post.blog-classic .pbmit-img-wrapper .pbmit-meta-date a:hover{
	color: var(--pbmit-global-color);
}
.blog-classic .pbmit-blog-classic-inner{
	background: 0 0;
    padding: 0;
    position: relative;
}
.blog-classic .pbmit-blog-classic-inner .pbmit-post-title{
	margin-bottom: 25px;
    padding-bottom: 20px;
	margin-top: 15px;
	line-height: 42px;
	border-bottom: 1px solid rgba(var(--pbmit-blackish-color-rgb),0.15);
}
.pbmit-blog-meta-top.pbmit-blog-meta,
.pbmit-blog-meta-top.pbmit-blog-meta a{
	color: #565656;
}
.pbmit-blog-meta-top .pbmit-meta a:hover{
	color: var(--pbmit-global-color);
}
.pbmit-blog-meta-top .pbmit-meta {
    display: inline-block;
    position: relative;
    font-size: 13px;
    text-transform: uppercase;
}
.pbmit-blog-meta-top .pbmit-meta.pbmit-meta-cat{
	padding-right: 30px;
}
.pbmit-blog-meta-top .pbmit-meta-cat .post-categories li{
	display: inline-block;
	margin: 0 5px 5px 0;
}
.blog-classic .pbmit-meta-cat a{
	font-size: 13px;
    line-height: 20px;
    display: inline-block;
    text-transform: uppercase;
    position: relative;
    letter-spacing: 0.5px;
    padding: 4px 15px;
    border-radius: 15px;
    color: var(--pbmit-white-color);
    background-color: var(--pbmit-global-color);
}
.blog-classic .pbmit-blog-meta-top .pbmit-meta-cat a:hover{
	color: var(--pbmit-white-color);
	background-color: var(--pbmit-blackish-color);
}
.pbmit-blog-meta-top .pbmit-meta-date{
	display: none;
}
.pbmit-blog-meta-top .pbmit-meta-author{
	margin-right: 15px;
	padding-right: 15px;
}
.pbmit-blog-meta-top .pbmit-meta-author:after{
	content: '|';
    position: absolute;
	padding: 0 15px;
}
.blog-classic .pbmit-blog-meta span.pbmit-meta.pbmit-meta-author::before,
.blog-classic .pbmit-blog-meta span.pbmit-meta:last-child::before{
	display: none;
}
.pbmit-blog-meta-top .pbmit-meta i {
    margin-right: 2px;
    font-size: 20px;
	color: var(--pbmit-global-color);
	display: none;
}

/** Blog Single **/
.blog-details .post.blog-classic{
	margin-bottom: 30px;
}
.blog-details .post.blog-classic .pbmit-blog-meta-top{
	margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(3, 27, 78, 0.1);
} 
.pbmit-firstletter::first-letter{
	padding: 0;
	font-size: 30px;
	line-height: 30px;
	float: left;
	margin-right: 10px;
	padding: 8px 10px;
	border-radius: 10px;
	color: var(--pbmit-white-color);
	background-color: var(--pbmit-blackish-color);
}
.blog-details .pbmit-entry-content blockquote{
	border: 0;
    padding: 40px 60px 40px 130px;
    font-size: 22px;
    line-height: 32px;
    font-weight: 600 !important;
    margin: 30px 0;
    position: relative;
    letter-spacing: 0;
    border-radius: 30px;
    color: var(--pbmit-blackish-color);
    background-color: var(--pbmit-light-color);
}
.blog-details .pbmit-entry-content blockquote:before{
	font-family: "pbminfotech-base-icons";
    content: "\e865";
    font-size: 22px;
    position: absolute;
    top: 25%;
    left: 35px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    border-radius: 100%;
    text-align: center;
    font-style: normal;
    color: var(--pbmit-global-color);
    background-color: var(--pbmit-white-color);
}
.blog-details .pbmit-entry-content blockquote p{
	margin-bottom: 0;
}
.blog-details .pbmit-entry-content:after{
	clear: both;
	content: "";
	display: table;
}
.blog-details .pbmit-entry-content blockquote cite{
	font-size: 14px;
    text-transform: uppercase;
    color:#565656;
	display: block;
	line-height: 24px;
    margin-top: 15px;
	letter-spacing: .5px;
	font-style: normal;
}
.blog-details .pbmit-entry-content blockquote cite:before{
	content: "-";
    margin-right: 5px;
}
.blog-details .pbmit-entry-content blockquote:after{
	visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}
.blog-details .pbmit-entry-content .pbmit-block-columns{
	display: flex;
	margin-bottom: 1.75em;
}
.blog-details .pbmit-entry-content .pbmit-block-columns img{
	margin-top: 15px;
	border-radius: 30px;
}
.blog-details .pbmit-entry-content .pbmit-block-column:first-child figure{
	padding: 0 10px 0 0;
}
.blog-details .pbmit-entry-content .pbmit-block-column:last-child figure{
	padding: 0 0 0 10px;
}
.blog-details .list-group .pbmit-icon-list-icon i,
.pbmit-portfolio-single .list-group .pbmit-icon-list-icon i,
.service-details .list-group .pbmit-icon-list-icon i,
.pbmit-team-single .list-group .pbmit-icon-list-icon i{
	font-size: 12px;
	line-height: 24px;
	margin-right: 15px;
}
.blog-details .list-group{
	padding-top: 20px;
}
.blog-details .list-group .list-group-item{
	align-items: start;
}
.blog-details .list-group .pbmit-icon-list-text{
	padding-left: 0;
	color: rgba(var(--pbmit-blackish-color-rgb), .7);
}
.blog-details .pbmit-blog-meta-bottom{
	margin-top: 15px;
}
.blog-details .post-navigation{
	border-top: 1px solid rgba(var(--pbmit-blackish-color-rgb),0.15);
    margin-top: 35px;
}
.blog-details .pbmit-author-box {
    position: relative;
    overflow: hidden;
    margin-top: 45px;
    margin-bottom: 0;
    padding: 40px;
    padding-right: 80px;
    background-color: var(--pbmit-blackish-color);
    display: flex;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    border-radius: 30px;
}
.blog-details .pbmit-author-image {
    width: 145px;
    min-width: 145px;
    height: 145px;
    line-height: 145px;
    display: block;
    overflow: hidden;
    margin-right: 50px;
    z-index: 1;
    text-align: center;
    border-radius: 50%;
    background-color: var(--pbmit-white-color);
    border: 4px solid var(--pbmit-global-color);
}
.blog-details .pbmit-author-image img{
	width: 100px;
	height: auto;
}
.blog-details .pbmit-author-content {
    -webkit-flex-shrink: 100;
    -moz-flex-shrink: 100;
    -ms-flex-negative: 100;
    flex-shrink: 100;
}
.blog-details .pbmit-author-content .pbmit-author-name {
    font-size: 22px;
	line-height: 28px;
    text-transform: capitalize;
    margin-bottom: 15px;
    display: inline-block;
}
.blog-details .pbmit-author-box a {
    color: var(--pbmit-white-color);
}
.blog-details .pbmit-author-box a:hover{
	color: var(--pbmit-global-color);
}
.blog-details .pbmit-author-content .pbmit-text {
    margin: 0;
	font-size: 16px;
    line-height: 26px;
    color: var(--pbmit-white-color);
}
.blog-details .comments-area{
	margin-top: 60px;
}
.blog-details .comments-area .comments-title{
	font-size: 36px;
	line-height: 42px;
	margin-bottom: 48px;
}
.comment-list .pbmit-comment-avatar{
	float: left;
}
.comment-list .pbmit-comment-avatar img{
    width: 90px;
    height: 90px;
	border-radius: 20px;
}
.blog-details .pbmit-comment-content{
	position: relative;
    padding: 0px 0px 30px 0;
    margin-left: 115px;
    min-height: 115px;
}
.blog-details .pbmit-comment-meta{
	display: flex;
    align-items: center;
}
.pbmit-comment-content .pbmit-comment-author {
	margin-top: 0;
    margin-bottom: 0;
    font-size: 16px;
    line-height: 24px;
	font-weight: 600;
    display: inline-flex;
    position: relative;
    color: var(--pbmit-global-color);
	font-family: var(--pbmit-heading-typography-font-family);
}
.blog-details .pbmit-comment-author-inner{
	margin: 0 5px;
    text-transform: capitalize;
}
.blog-details .pbmit-comment-author-inner a{
	color: var(--pbmit-blackish-color);
}
.blog-details .pbmit-comment-author-inner a:hover{
	color: var(--pbmit-global-color);
}
.blog-details .pbmit-comment-date{
	display: block;
    font-size: 14px;
	padding: 0 0 0 30px;
}
.blog-details .pbmit-comment-date a{
	font-size: 14px;
    line-height: 24px;
    position: relative;
	color: var(--pbmit-global-color);
}
.blog-details .pbmit-comment-content p{
	margin: 10px 0 18px;
    padding: 25px 60px 35px 25px;
    border-radius: 15px;
    position: relative;
    border: 1px solid rgba(var(--pbmit-blackish-color-rgb), 0.15);
}
.blog-details .pbmit-comment-content p:before{
	content: '';
    height: 20px;
    width: 20px;
    position: absolute;
    background-color: var(--pbmit-white-color);
    top: 30px;
    left: -11px;
    border-top: 1px solid rgba(var(--pbmit-blackish-color-rgb), 0.15);
    border-left: 1px solid rgba(var(--pbmit-blackish-color-rgb), 0.15);
    transform: rotate(-45deg);
}
.blog-details .pbmit-comment-content .reply a{
	display: inline-block;
	position: relative;
    font-size: 14px;
    line-height: 24px;
    text-transform: capitalize;
    letter-spacing: .5px;
    -webkit-transition: 0.2s ease-out 0s;
    -o-transition: 0.2s ease-out 0s;
    transition: 0.2s ease-out 0s;
    color: var(--pbmit-blackish-color);
}
.blog-details .pbmit-comment-content .reply a::after{
	font-family: 'pbminfotech-base-icons';
    content: "\e8dd";
    font-style: normal;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    text-align: center;
    opacity: 1;
    font-variant: normal;
    text-transform: none;
    line-height: 10px;
    font-size: 10px;
    margin-left: 6px;
    -webkit-transition: 0.2s ease-out 0s;
    -o-transition: 0.2s ease-out 0s;
    transition: 0.2s ease-out 0s;
}
.blog-details .pbmit-comment-content .reply a:hover{
	color: var(--pbmit-global-color);
}
.blog-details .pbmit-comment-content .reply a:hover::after{
	transform: rotate(45deg);
}
.comment-list .children{
	margin-left: 100px;
    padding-top: 8px;
}
.blog-details .comment-respond{
	background-color: var(--pbmit-light-color);
    padding: 60px 70px;
    border-radius: 30px;
    margin-top: 0px;
    margin-bottom: 20px;
}
.blog-details .comment-respond .comment-reply-title{
	margin-bottom: 25px;
}
.blog-details .comment-respond .comment-notes{
	font-size: 15px;
}
.blog-details .comment-respond .comment-form .form-check{
	margin-bottom: 25px;
}
.blog-details .comment-respond .comment-form .form-check label{
	font-size: 15px;
}
.blog-details .comment-respond .comment-form form button{
	padding: 13px 35px;
}

/** 05 - Team Single **/
.pbmit-team-single .pbmit-team-left-inner{
	position: relative;
    z-index: 1;
    margin: 0;
    border-radius: 30px;
	overflow: hidden;
    border: 1px solid rgba(var(--pbmit-blackish-color-rgb), 0.15);
}
.pbmit-team-single .pbmit-featured-wrapper img {
    width: 100%;
}
.pbmit-team-single .pbmit-team-detail{
	position: relative;
    padding: 45px 38px 70px;
}
.pbmit-team-single .pbmit-team-detail .pbmit-team-summary{
	padding-bottom: 20px;
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(var(--pbmit-blackish-color-rgb), 0.1);
}
.pbmit-team-single .pbmit-team-designation {
    font-size: 14px;
    line-height: 24px;
    text-transform: capitalize;
    letter-spacing: 0.5px;
    position: relative;
    color: var(--pbmit-global-color);
}
.pbmit-team-single .pbmit-team-designation:before{
	content: '/';
    padding-right: 5px;
    display: inline-block;
}
.pbmit-team-single .pbmit-short-description{
	margin-bottom: 20px;
}
.pbmit-team-single .pbmit-team-title {
    font-size: 26px;
    line-height: 32px;
    margin: 0;
}
.pbmit-single-team-info{
	position: relative;
}
.pbmit-single-team-info li,
.pbmit-single-team-info li a{
	font-size: 16px;
    line-height: 16px;
    color: #565656;
}
.pbmit-single-team-info li:not(:last-child){
	margin-bottom: 20px;
}
.pbmit-single-team-info li label{
	font-size: 16px;
    line-height: 26px;
    display: block;
    text-transform: capitalize;
    color: var(--pbmit-global-color);
}
.pbmit-team-single .pbmit-team-share-btn {
    position: absolute;
    right: 40px;
    bottom: 0;
}
.pbmit-team-single .pbmit-share-icon {
    width: 60px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    display: inline-block;
    position: relative;
    cursor: pointer;
    border-radius: 50px 50px 0 0;
    color: var(--pbmit-blackish-color);
    background-color: var(--pbmit-light-color);
    transition: all 0.3s ease-in-out;
}
.pbmit-team-single .pbmit-share-icon:before,
.pbmit-team-single .pbmit-share-icon:after{
	content: "";
    position: absolute;
    background-color: transparent;
    bottom: 0;
    height: 40px;
    width: 20px;
    box-shadow: 0 20px 0 0 var(--pbmit-light-color);
    transition: all 0.3s ease-in-out;
}
.pbmit-team-single .pbmit-share-icon:before{
	right: 100%;
    border-bottom-right-radius: 20px;
}
.pbmit-team-single .pbmit-share-icon:after{
	left: 100%;
    border-bottom-left-radius: 20px;
}
.pbmit-team-single .pbmit-share-icon i {
    transition: all 0.3s ease-in-out;
    display: block;
    font-size: 20px;
}
.pbmit-team-single .pbmit-team-box-links {
    text-align: center;
    position: absolute;
    bottom: 25px;
    right: 10px;
    transform: translateY(10px);
    opacity: 0;
    padding: 0;
}
.pbmit-team-single .pbmit-team-box-links .pbmit-team-social-links {
    display: flex;
    flex-direction: column-reverse;
}
.pbmit-team-single .pbmit-team-box-links ul li{
	display: block;
    margin: 0;
    padding: 0;
    margin-bottom: 5px;
    transform: translate(0);
    transition: all 600ms ease-in-out;
}
.pbmit-team-single .pbmit-team-box-links ul li:nth-child(1){
	transform: translate(0, 0);
}
.pbmit-team-single .pbmit-team-social-links li:nth-child(2) {
    transform: translate(0, 43px);
}
.pbmit-team-single .pbmit-team-social-links li:nth-child(3) {
    transform: translate(0, 86px);
}
.pbmit-team-single .pbmit-team-social-links li:last-child {
    transform: translate(0, 129px);
}
.pbmit-team-single .pbmit-team-box-links ul li a{
	display: inline-block;
    opacity: 1;
	font-size: 16px;
	position: relative;
	text-align: center;
	width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    color: var(--pbmit-white-color);
    background-color: var(--pbmit-global-color);
    transition: all 600ms ease-in-out;
}
.pbmit-team-single .pbmit-team-share-btn:hover .pbmit-share-icon {
    color: var(--pbmit-white-color);
    background-color: var(--pbmit-blackish-color);
    transition: all 0.3s ease-in-out;
}
.pbmit-team-single .pbmit-team-share-btn:hover .pbmit-share-icon:before,
.pbmit-team-single .pbmit-team-share-btn:hover .pbmit-share-icon:after{
	box-shadow: 0 20px 0 0 var(--pbmit-blackish-color);
}
.pbmit-team-single .pbmit-team-share-btn:hover .pbmit-share-icon i{
	transform: rotateY(180deg);
}
.pbmit-team-single .pbmit-team-share-btn:hover .pbmit-team-box-links{
	transform: translateY(-25px);
    opacity: 1;
}
.pbmit-team-single .pbmit-team-share-btn:hover .pbmit-team-social-links li {
    transform: translate(0);
}
.pbmit-team-single .pbmit-entry-content .progressbar{
	padding-top: 30px;
}
.pbmit-team-single .list-group-borderless .list-group-item:not(:last-child){
	padding-bottom: calc(10px/2);
}
.pbmit-team-single .comment-respond{
	margin-top: 50px;
	padding: 60px 70px 0px 70px;
	border-radius: 30px;
	background-color: var(--pbmit-light-color);
}
.pbmit-team-single .comment-respond form .form-control{
	font-weight: 600;
}
.pbmit-team-single .comment-respond form .form-control::placeholder{
	color: rgba(var(--pbmit-blackish-color-rgb), .3);
}
.pbmit-team-single .comment-respond form .form-control:focus::placeholder{
	color: rgba(var(--pbmit-blackish-color-rgb), .6);
}
.pbmit-team-single .comment-form form .form-check{
	margin-bottom: 20px;
}

/** 06 - Contact Us **/
.contact-us-bg{
	background-image: url(../images/bg/about-bg-icon.png);
    background-position: -45px 120px;
    background-repeat: no-repeat;
}
.contact-form-rightbox{
	padding: 60px 60px 0px 60px;
	background-color: var(--pbmit-light-color);
	border-radius: 30px;
	margin-left: 45px;
}
.contact-form-rightbox form input,
.contact-form-rightbox form textarea{
	font-family: inherit;
	font-weight: 600;
}
.contact-form-rightbox form input::placeholder,
.contact-form-rightbox form textarea::placeholder{
	color: rgba(var(--pbmit-blackish-color-rgb), .3);
}
.contact-form-rightbox form,
.pbmit-team-single .comment-form form{
	position: relative;
	padding-bottom: 60px;
}
.contact-form-rightbox form button,
.pbmit-team-single .comment-form form button{
	position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    padding: 18px 36px;
    border-radius: 30px 30px 0 0;
    color: var(--pbmit-blackish-color);
    background-color: var(--pbmit-white-color);
}
.contact-form-rightbox form button:after,
.contact-form-rightbox form button:before,
.pbmit-team-single .comment-form form button:after,
.pbmit-team-single .comment-form form button:before{
	content: "";
    position: absolute;
    background-color: transparent;
    bottom: 0;
    height: 40px;
    width: 20px;
    box-shadow: 0 20px 0 0 var(--pbmit-white-color);
    transition: all 0.3s ease-in-out;
}
.contact-form-rightbox form button:before,
.pbmit-team-single .comment-form form button:before{
	right: 100%;
    border-bottom-right-radius: 20px;
}
.contact-form-rightbox form button:after,
.pbmit-team-single .comment-form form button:after{
	left: 100%;
    border-bottom-left-radius: 20px;
}
.contact-form-rightbox form .pbmit-btn svg path,
.pbmit-team-single .comment-form form button svg path{
	stroke: var(--pbmit-blackish-color);
}
.contact-form-rightbox form .pbmit-btn:hover,
.pbmit-team-single .comment-form form button:hover{
	color: var(--pbmit-global-color);
	background-color: var(--pbmit-white-color);
}
.iframe-section iframe{
	height: 600px;
    filter: brightness(91%) contrast(95%) saturate(116%) blur(0px) hue-rotate(0deg);
	max-width: 100%;
    width: 100%;
    margin: 0;
    line-height: 1;
    border: none;
}
label.error {
	background: red;
	color: white;
	border-radius: 0px 0 10px 10px;
	margin-bottom: 15px;
	padding: 0 10px;
	font-size: 12px;
	display: block;
	margin-top: 0px;
	margin-right: 20%;
}
.message-status{
	margin-top: 30px;
}
.form-control.error{
	margin-bottom: 0 !important;
}

/**07 - Comment Form **/
.form-control,
form select{
	display: block;
    border: 1px solid rgba(var(--pbmit-blackish-color-rgb),.2);
    background-color: transparent;
    height: 60px;
    font-size: 14px;
    padding: 0 50px 0 35px;
    border-radius: 50px;
    margin-bottom: 20px;
}
.form-control{
	font-weight: 400;
}
.form-control:focus{
	border-color: rgba(var(--pbmit-blackish-color-rgb),.8);
	background-color: transparent;
}
form select{
	width: 100%;
	cursor: pointer;
	font-weight: 500;
	color: #565656;
	text-overflow: ellipsis;
	white-space: nowrap;
	border: 1px solid rgba(var(--pbmit-secondary-color-rgb),.2);
}
form select:focus{
	outline: none;
}
.pbmit-bg-color-blackish form select,
.bg-blackish form select{
	color: #7892ae;
	border-color: rgba(var(--pbmit-white-color-rgb),.2) !important;
}
.pbmit-bg-color-blackish .form-control,
.bg-blackish .form-control{
	border-color: rgba(var(--pbmit-white-color-rgb),.2);
}
.pbmit-bg-color-blackish .form-control:focus,
.bg-blackish .form-control:focus{
	color: var(--pbmit-white-color);
	border-color: rgba(var(--pbmit-white-color-rgb),.8);
}
.pbmit-bg-color-blackish .form-control::placeholder,
.bg-blackish .form-control::placeholder{
	color: #7892ae;
}
form .input-text-group{
	font-size: 15px;
    font-style: italic;
    color: #d4e0f0;
}
form textarea.form-control {
    height: 110px;
    resize: none;
    padding: 20px;
    border-radius: 20px;
}
